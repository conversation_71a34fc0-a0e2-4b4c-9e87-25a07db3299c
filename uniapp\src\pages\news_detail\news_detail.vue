<template>
    <page-meta :page-style="$theme.pageStyle">
        <!-- #ifndef H5 -->
        <navigation-bar
            :front-color="$theme.navColor"
            :background-color="$theme.navBgColor"
        />
        <!-- #endif -->
    </page-meta>
    <view class="news-detail bg-white">
        <!-- 标题信息 -->
        <view class="news-detail-header py-[20rpx] px-[30rpx]">
            <view class="text-3xl font-medium">{{ newsData.title }}</view>
            <view class="flex mt-[20rpx] text-xs">
                <view class="mr-[40rpx]" v-if="newsData.author">作者: {{ newsData.author }}</view>
                <view class="text-muted mr-[40rpx] flex-1">{{ newsData.create_time }}</view>
                <view class="flex items-center text-muted flex-none">
                    <image
                        src="/static/images/icon/icon_visit.png"
                        class="w-[30rpx] h-[30rpx]"
                    ></image>
                    <view class="ml-[10rpx]">{{ newsData.click }}</view>
                </view>
            </view>
        </view>

        <!-- 资讯内容 -->
        <view class="news-detail-section bg-white p-[24rpx]">
            <!-- 摘要 -->
            <view class="summary p-[20rpx] text-base" v-if="newsData.abstract">
                <text class="font-medium">摘要: </text> {{ newsData.abstract }}
            </view>
            <!-- 内容 -->
            <view class="mt-[20rpx]">
                <u-parse :html="newsData.content"></u-parse>
            </view>
        </view>

        <view class="panel-btn flex items-center px-[34rpx]" @click="handleAddCollect(newsData.id)">
            <u-icon
                :name="newsData.collect ? 'star-fill' : 'star'"
                size="40"
                :color="newsData.collect ? '#F7BA47' : '#333'"
            ></u-icon>
            <text class="ml-[10rpx]">收藏</text>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getArticleDetail, addCollect, cancelCollect } from '@/api/news'

const newsData = ref<any>({})
let newsId = ''

const getData = async (id) => {
    newsData.value = await getArticleDetail({ id })
}

const handleAddCollect = async (id: number) => {
    try {
        if (newsData.value.collect) {
            await cancelCollect({ id })
            uni.$u.toast('已取消收藏')
        } else {
            await addCollect({ id })
            uni.$u.toast('收藏成功')
        }
        getData(newsId)
    } catch (e) {
        //TODO handle the exception
    }
}

onLoad((options: any) => {
    newsId = options.id
    getData(newsId)
})
</script>

<style lang="scss" scoped>
.news-detail {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

.news-header {
    padding: 30rpx;
    position: relative;
    background-image: linear-gradient(to right, v-bind('$theme.primaryColor'), v-bind('$theme.minorColor'));
    border-bottom-left-radius: 30rpx;
    border-bottom-right-radius: 30rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.news-category {
    display: inline-block;
    padding: 8rpx 20rpx;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #fff;
}

.news-content {
    flex: 1;
    margin: 30rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.news-title-section {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.news-title {
    font-size: 36rpx;
    font-weight: bold;
    line-height: 1.4;
    margin-bottom: 20rpx;
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    font-size: 24rpx;
    color: #999;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6rpx;
}

.news-abstract {
    margin: 30rpx;
    padding: 20rpx 30rpx;
    background-color: #f9f9f9;
    border-radius: 12rpx;
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
    border-left: 8rpx solid v-bind('$theme.primaryColor');
}

.news-body {
    padding: 30rpx;
    font-size: 28rpx;
    line-height: 1.8;
    color: #333;
}

.news-actions {
    display: flex;
    justify-content: center;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
}

.collect-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10rpx;
    padding: 16rpx 40rpx;
    border-radius: 40rpx;
    border: 2rpx solid v-bind('$theme.primaryColor');
    color: v-bind('$theme.primaryColor');
    transition: all 0.3s;
}

.news-navigation {
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
}

.nav-item {
    padding: 20rpx 0;
    display: flex;
    align-items: center;
}

.nav-label {
    font-size: 26rpx;
    color: #999;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.nav-title {
    font-size: 26rpx;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nav-item:active {
    background-color: #f5f5f5;
}
</style>
