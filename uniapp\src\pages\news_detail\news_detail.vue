<template>
    <page-meta :page-style="$theme.pageStyle">
        <!-- #ifndef H5 -->
        <navigation-bar
            :front-color="$theme.navColor"
            :background-color="$theme.navBgColor"
        />
        <!-- #endif -->
    </page-meta>
    <view class="news-detail">
        <!-- 科技感头部区域 -->
        <view class="news-header" :style="'background: url(/static/images/user/my_topbg.png), linear-gradient(90deg, '+$theme.primaryColor+', '+$theme.minorColor+');'">
            <view class="header-content">
                <view class="news-title">{{ newsData.title }}</view>
                <view class="news-meta">
                    <view class="meta-item" v-if="newsData.author">
                        <uni-icons type="person" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
                        <text>{{ newsData.author }}</text>
                    </view>
                    <view class="meta-item">
                        <uni-icons type="calendar" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
                        <text>{{ newsData.create_time }}</text>
                    </view>
                    <view class="meta-item">
                        <uni-icons type="eye" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
                        <text>{{ newsData.click }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 主要内容区域 -->
        <view class="news-content">
            <!-- 摘要卡片 -->
            <view class="news-abstract" v-if="newsData.abstract">
                <view class="abstract-header">
                    <view class="abstract-icon">
                        <uni-icons type="info" size="18" :color="$theme.primaryColor"></uni-icons>
                    </view>
                    <text class="abstract-title">内容摘要</text>
                </view>
                <view class="abstract-content">{{ newsData.abstract }}</view>
            </view>

            <!-- 正文内容 -->
            <view class="news-body">
                <u-parse :html="newsData.content"></u-parse>
            </view>
        </view>

        <!-- 底部操作区域 -->
        <view class="news-actions">
            <view class="collect-btn"
                  :class="{ 'collected': newsData.collect }"
                  :style="'border-color: '+$theme.primaryColor+'; color: '+(newsData.collect ? '#fff' : $theme.primaryColor)+'; background-color: '+(newsData.collect ? $theme.primaryColor : 'transparent')+';'"
                  @click="handleAddCollect(newsData.id)">
                <uni-icons
                    :type="newsData.collect ? 'star-filled' : 'star'"
                    size="20"
                    :color="newsData.collect ? '#fff' : $theme.primaryColor">
                </uni-icons>
                <text>{{ newsData.collect ? '已收藏' : '收藏' }}</text>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getArticleDetail, addCollect, cancelCollect } from '@/api/news'

const newsData = ref<any>({})
let newsId = ''

const getData = async (id) => {
    newsData.value = await getArticleDetail({ id })
}

const handleAddCollect = async (id: number) => {
    try {
        if (newsData.value.collect) {
            await cancelCollect({ id })
            uni.$u.toast('已取消收藏')
        } else {
            await addCollect({ id })
            uni.$u.toast('收藏成功')
        }
        getData(newsId)
    } catch (e) {
        //TODO handle the exception
    }
}

onLoad((options: any) => {
    newsId = options.id
    getData(newsId)
})
</script>

<style lang="scss" scoped>
.news-detail {
    min-height: 100vh;
    background-color: #f6f6f6;
    display: flex;
    flex-direction: column;
}

// 科技感头部样式
.news-header {
    position: relative;
    padding: 40rpx 30rpx 60rpx;
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: 100%;
    border-bottom-left-radius: 40rpx;
    border-bottom-right-radius: 40rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
        z-index: 1;
    }
}

.header-content {
    position: relative;
    z-index: 2;
}

.news-title {
    font-size: 40rpx;
    font-weight: bold;
    line-height: 1.4;
    color: #fff;
    margin-bottom: 30rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 30rpx;
    font-size: 26rpx;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
    color: rgba(255, 255, 255, 0.9);

    text {
        font-size: 26rpx;
    }
}

// 主要内容区域
.news-content {
    flex: 1;
    margin: -20rpx 20rpx 20rpx;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    z-index: 3;
}

// 摘要卡片样式
.news-abstract {
    margin: 30rpx;
    padding: 24rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-radius: 16rpx;
    border: 1rpx solid rgba(74, 93, 255, 0.1);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 6rpx;
        background: linear-gradient(to bottom, v-bind('$theme.primaryColor'), v-bind('$theme.minorColor'));
        border-radius: 3rpx;
    }
}

.abstract-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.abstract-icon {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    background-color: rgba(74, 93, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;
}

.abstract-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
}

.abstract-content {
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
}

// 正文样式
.news-body {
    padding: 30rpx;
    font-size: 30rpx;
    line-height: 1.8;
    color: #333;
}

// 底部操作区域
.news-actions {
    display: flex;
    justify-content: center;
    padding: 30rpx;
    background-color: #fff;
    border-top: 1rpx solid #f0f0f0;
}

.collect-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    padding: 20rpx 48rpx;
    border-radius: 50rpx;
    border: 2rpx solid;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
    }

    &.collected {
        box-shadow: 0 4rpx 16rpx rgba(74, 93, 255, 0.3);
    }
}
</style>
