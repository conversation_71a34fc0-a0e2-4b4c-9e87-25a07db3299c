<template>
    <page-meta :page-style="$theme.pageStyle">
        <!-- #ifndef H5 -->
        <navigation-bar
            :front-color="$theme.navColor"
            :background-color="$theme.navBgColor"
        />
        <!-- #endif -->
    </page-meta>
    <view class="news-detail">
        <!-- 科技感头部区域 -->
        <view class="news-header" :style="'background: url(/static/images/user/my_topbg.png), linear-gradient(90deg, '+$theme.primaryColor+', '+$theme.minorColor+');'">
            <view class="header-content">
                <view class="news-title">{{ newsData.title }}</view>
                <view class="news-meta">
                    <view class="meta-item" v-if="newsData.author">
                        <uni-icons type="person" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
                        <text>{{ newsData.author }}</text>
                    </view>
                    <view class="meta-item">
                        <uni-icons type="calendar" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
                        <text>{{ newsData.create_time }}</text>
                    </view>
                    <view class="meta-item">
                        <uni-icons type="eye" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
                        <text>{{ newsData.click }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 主要内容区域 -->
        <view class="news-content">
            <!-- 摘要卡片 -->
            <view class="news-abstract" v-if="newsData.abstract">
                <view class="abstract-header">
                    <view class="abstract-icon">
                        <uni-icons type="info" size="18" :color="$theme.primaryColor"></uni-icons>
                    </view>
                    <text class="abstract-title">内容摘要</text>
                </view>
                <view class="abstract-content">{{ newsData.abstract }}</view>
            </view>

            <!-- 正文内容 -->
            <view class="news-body">
                <u-parse :html="newsData.content"></u-parse>
            </view>
        </view>

        <!-- 悬浮收藏按钮 -->
        <view
            class="floating-btn collect-float"
            :class="{ 'collected': newsData.collect }"
            @click="handleAddCollect(newsData.id)"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="handleTouchEnd"
            :style="{
                right: collectBtnRight + 'rpx',
                bottom: collectBtnBottom + 'rpx',
                backgroundColor: newsData.collect ? $theme.primaryColor : 'rgba(0, 0, 0, 0.6)'
            }"
        >
            <uni-icons
                :type="newsData.collect ? 'star-filled' : 'star'"
                size="28"
                color="#fff">
            </uni-icons>
        </view>

        <!-- 返回顶部悬浮按钮 -->
        <view
            v-if="showBackToTop"
            class="floating-btn top-float"
            @click="backToTop"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="handleTouchEnd"
            :style="{ right: backToTopBtnRight + 'rpx', bottom: backToTopBtnBottom + 'rpx' }"
        >
            <uni-icons type="top" size="28" color="#fff"></uni-icons>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import { getArticleDetail, addCollect, cancelCollect } from '@/api/news'

const newsData = ref<any>({})
let newsId = ''

// 悬浮按钮位置和状态
const collectBtnRight = ref(30)
const collectBtnBottom = ref(230)
const backToTopBtnRight = ref(30)
const backToTopBtnBottom = ref(130)
const showBackToTop = ref(false)
const isDragging = ref(false)
const startX = ref(0)
const startY = ref(0)
const startRight = ref(0)
const startBottom = ref(0)
const windowWidth = ref(375)
const windowHeight = ref(667)

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    windowWidth.value = systemInfo.windowWidth
    windowHeight.value = systemInfo.windowHeight
})

// 页面滚动监听
onPageScroll((e) => {
    showBackToTop.value = e.scrollTop > 300
})

const getData = async (id) => {
    newsData.value = await getArticleDetail({ id })
}

const handleAddCollect = async (id: number) => {
    try {
        if (newsData.value.collect) {
            await cancelCollect({ id })
            uni.$u.toast('已取消收藏')
        } else {
            await addCollect({ id })
            uni.$u.toast('收藏成功')
        }
        getData(newsId)
    } catch (e) {
        //TODO handle the exception
    }
}

// 返回顶部
const backToTop = () => {
    uni.vibrateShort() // 添加振动反馈
    uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
    })
}

// 悬浮按钮拖拽功能
const handleTouchStart = (e) => {
    isDragging.value = true
    const touch = e.touches && e.touches[0] ? e.touches[0] : null
    if (touch) {
        startX.value = touch.clientX
        startY.value = touch.clientY
    }
    const targetClass = e.currentTarget.classList
    if (targetClass.contains('collect-float')) {
        startRight.value = collectBtnRight.value
        startBottom.value = collectBtnBottom.value
    } else if (targetClass.contains('top-float')) {
        startRight.value = backToTopBtnRight.value
        startBottom.value = backToTopBtnBottom.value
    }
}

const handleTouchMove = (e) => {
    if (!isDragging.value) return

    const touch = e.touches && e.touches[0] ? e.touches[0] : null
    if (!touch) return

    const currentX = touch.clientX
    const currentY = touch.clientY
    const deltaX = startX.value - currentX
    const deltaY = startY.value - currentY
    const targetClass = e.currentTarget.classList

    if (targetClass.contains('collect-float')) {
        let newRight = startRight.value + deltaX / 2
        let newBottom = startBottom.value + deltaY / 2

        newRight = Math.max(20, Math.min(newRight, windowWidth.value - 80))
        newBottom = Math.max(20, Math.min(newBottom, windowHeight.value - 80))

        collectBtnRight.value = newRight
        collectBtnBottom.value = newBottom
    } else if (targetClass.contains('top-float')) {
        let newRight = startRight.value + deltaX / 2
        let newBottom = startBottom.value + deltaY / 2

        newRight = Math.max(20, Math.min(newRight, windowWidth.value - 80))
        newBottom = Math.max(20, Math.min(newBottom, windowHeight.value - 80))

        backToTopBtnRight.value = newRight
        backToTopBtnBottom.value = newBottom
    }
}

const handleTouchEnd = () => {
    isDragging.value = false
}

onLoad((options: any) => {
    newsId = options.id
    getData(newsId)
})
</script>

<style lang="scss" scoped>
.news-detail {
    min-height: 100vh;
    background-color: #f6f6f6;
    display: flex;
    flex-direction: column;
}

// 科技感头部样式（移除圆角）
.news-header {
    position: relative;
    padding: 40rpx 30rpx 80rpx;
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: 100%;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
        z-index: 1;
    }
}

.header-content {
    position: relative;
    z-index: 2;
}

.news-title {
    font-size: 40rpx;
    font-weight: bold;
    line-height: 1.4;
    color: #fff;
    margin-bottom: 30rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 30rpx;
    font-size: 26rpx;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
    color: rgba(255, 255, 255, 0.9);

    text {
        font-size: 26rpx;
    }
}

// 主要内容区域
.news-content {
    flex: 1;
    margin: -40rpx 20rpx 20rpx;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    z-index: 3;
}

// 摘要卡片样式
.news-abstract {
    margin: 30rpx;
    padding: 24rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-radius: 16rpx;
    border: 1rpx solid rgba(74, 93, 255, 0.1);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 6rpx;
        background: linear-gradient(to bottom, v-bind('$theme.primaryColor'), v-bind('$theme.minorColor'));
        border-radius: 3rpx;
    }
}

.abstract-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.abstract-icon {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    background-color: rgba(74, 93, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;
}

.abstract-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
}

.abstract-content {
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
}

// 正文样式
.news-body {
    padding: 30rpx;
    font-size: 30rpx;
    line-height: 1.8;
    color: #333;
}

// 悬浮按钮样式
.floating-btn {
    position: fixed;
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
    z-index: 999;
    transition: all 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);

    &:active {
        transform: scale(0.9);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    }

    &.collect-float {
        right: 30rpx;
        bottom: 230rpx;
        background: linear-gradient(135deg, v-bind('$theme.primaryColor'), v-bind('$theme.minorColor'));

        &.collected {
            box-shadow: 0 6rpx 20rpx rgba(74, 93, 255, 0.4);
        }
    }

    &.top-float {
        background: rgba(0, 0, 0, 0.6);
        right: 30rpx;
        bottom: 130rpx;
    }
}
</style>
